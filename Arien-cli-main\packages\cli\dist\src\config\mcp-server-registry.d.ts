/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */
import { MCPServerConfig } from '@arien/arien-cli-core';
export interface MCPServerRegistryEntry {
    name: string;
    config: MCPServerConfig;
    verified: boolean;
    lastVerified: Date;
    category: string;
    tags: string[];
    minimumRequirements?: {
        commands?: string[];
        environment?: Record<string, string>;
    };
}
/**
 * Registry of verified MCP servers with automatic updates and validation
 */
export declare class MCPServerRegistry {
    private static readonly VERIFIED_SERVERS;
    private static readonly EXPERIMENTAL_SERVERS;
    private static readonly CONFIGURABLE_SERVERS;
    /**
     * Gets all verified MCP servers that should be included by default
     */
    static getVerifiedServers(): Record<string, MCPServerConfig>;
    /**
     * Gets experimental servers (not included by default)
     */
    static getExperimentalServers(): Record<string, MCPServerConfig>;
    /**
     * Gets servers that require configuration (API keys, etc.)
     */
    static getConfigurableServers(): Record<string, MCPServerConfig>;
    /**
     * Gets a specific server entry with metadata
     */
    static getServerEntry(name: string): MCPServerRegistryEntry | undefined;
    /**
     * Gets all servers by category
     */
    static getServersByCategory(category: string): MCPServerRegistryEntry[];
    /**
     * Gets all servers with specific tags
     */
    static getServersByTags(tags: string[]): MCPServerRegistryEntry[];
    /**
     * Checks if a server meets system requirements
     */
    static checkServerRequirements(serverName: string): Promise<{
        meetsRequirements: boolean;
        missingRequirements: string[];
    }>;
    /**
     * Gets installation instructions for a server
     */
    static getInstallationInstructions(serverName: string): string[];
    /**
     * Validates and filters servers based on system capabilities
     */
    static getCompatibleServers(): Promise<Record<string, MCPServerConfig>>;
}

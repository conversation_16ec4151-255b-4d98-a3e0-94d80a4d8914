{"version": 3, "file": "mcp-server-health.js", "sourceRoot": "", "sources": ["../../../src/config/mcp-server-health.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAIH,MAAM,CAAN,IAAY,qBAKX;AALD,WAAY,qBAAqB;IAC/B,4CAAmB,CAAA;IACnB,gDAAuB,CAAA;IACvB,4CAAmB,CAAA;IACnB,8CAAqB,CAAA;AACvB,CAAC,EALW,qBAAqB,KAArB,qBAAqB,QAKhC;AAkBD;;GAEG;AACH,MAAM,OAAO,sBAAsB;IAWb;IAVZ,SAAS,GAAG,IAAI,GAAG,EAA2B,CAAC;IAC/C,oBAAoB,GAAG,IAAI,GAAG,EAA0B,CAAC;IAEhD,cAAc,GAAuB;QACpD,UAAU,EAAE,CAAC;QACb,UAAU,EAAE,IAAI,EAAE,WAAW;QAC7B,mBAAmB,EAAE,KAAK,EAAE,aAAa;QACzC,oBAAoB,EAAE,CAAC;KACxB,CAAC;IAEF,YAAoB,UAAuC,EAAE;QAAzC,YAAO,GAAP,OAAO,CAAkC;QAC3D,IAAI,CAAC,OAAO,GAAG,EAAE,GAAG,IAAI,CAAC,cAAc,EAAE,GAAG,OAAO,EAAE,CAAC;IACxD,CAAC;IAED;;OAEG;IACH,cAAc,CAAC,IAAY,EAAE,MAAuB;QAClD,MAAM,MAAM,GAAoB;YAC9B,IAAI;YACJ,MAAM,EAAE,qBAAqB,CAAC,OAAO;YACrC,SAAS,EAAE,IAAI,IAAI,EAAE;YACrB,mBAAmB,EAAE,CAAC;YACtB,MAAM;SACP,CAAC;QAEF,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;QACjC,OAAO,CAAC,KAAK,CAAC,0BAA0B,IAAI,yBAAyB,CAAC,CAAC;IACzE,CAAC;IAED;;OAEG;IACH,kBAAkB,CAChB,IAAY,EACZ,MAA6B,EAC7B,KAAc;QAEd,MAAM,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;QACxC,IAAI,CAAC,MAAM,EAAE,CAAC;YACZ,OAAO,CAAC,IAAI,CAAC,sDAAsD,IAAI,EAAE,CAAC,CAAC;YAC3E,OAAO;QACT,CAAC;QAED,MAAM,cAAc,GAAG,MAAM,CAAC,MAAM,CAAC;QACrC,MAAM,CAAC,MAAM,GAAG,MAAM,CAAC;QACvB,MAAM,CAAC,SAAS,GAAG,IAAI,IAAI,EAAE,CAAC;QAE9B,IAAI,MAAM,KAAK,qBAAqB,CAAC,SAAS,EAAE,CAAC;YAC/C,MAAM,CAAC,mBAAmB,EAAE,CAAC;YAC7B,MAAM,CAAC,SAAS,GAAG,KAAK,CAAC;YAEzB,8CAA8C;YAC9C,IAAI,MAAM,CAAC,mBAAmB,IAAI,IAAI,CAAC,OAAO,CAAC,oBAAqB,EAAE,CAAC;gBACrE,MAAM,CAAC,MAAM,GAAG,qBAAqB,CAAC,QAAQ,CAAC;gBAC/C,OAAO,CAAC,IAAI,CACV,eAAe,IAAI,oBAAoB,MAAM,CAAC,mBAAmB,yBAAyB;oBAC1F,eAAe,KAAK,EAAE,CACvB,CAAC;gBACF,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;YAC7B,CAAC;QACH,CAAC;aAAM,IAAI,MAAM,KAAK,qBAAqB,CAAC,OAAO,EAAE,CAAC;YACpD,MAAM,CAAC,mBAAmB,GAAG,CAAC,CAAC;YAC/B,MAAM,CAAC,SAAS,GAAG,SAAS,CAAC;YAE7B,8CAA8C;YAC9C,IAAI,cAAc,KAAK,qBAAqB,CAAC,QAAQ,EAAE,CAAC;gBACtD,OAAO,CAAC,IAAI,CAAC,eAAe,IAAI,4BAA4B,CAAC,CAAC;gBAC9D,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC;YAC9B,CAAC;QACH,CAAC;QAED,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;IACnC,CAAC;IAED;;OAEG;IACH,eAAe,CAAC,IAAY;QAC1B,OAAO,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;IAClC,CAAC;IAED;;OAEG;IACH,kBAAkB;QAChB,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC,CAAC;IAC7C,CAAC;IAED;;OAEG;IACH,iBAAiB;QACf,MAAM,cAAc,GAAoC,EAAE,CAAC;QAE3D,KAAK,MAAM,CAAC,IAAI,EAAE,MAAM,CAAC,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;YAC5C,IAAI,MAAM,CAAC,MAAM,KAAK,qBAAqB,CAAC,OAAO;gBAC/C,MAAM,CAAC,MAAM,KAAK,qBAAqB,CAAC,OAAO,EAAE,CAAC;gBACpD,cAAc,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC;YACvC,CAAC;QACH,CAAC;QAED,OAAO,cAAc,CAAC;IACxB,CAAC;IAED;;OAEG;IACH,gBAAgB,CAAC,IAAY;QAC3B,IAAI,IAAI,CAAC,oBAAoB,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC;YACxC,OAAO,CAAC,kBAAkB;QAC5B,CAAC;QAED,MAAM,QAAQ,GAAG,WAAW,CAAC,GAAG,EAAE;YAChC,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC;QAChC,CAAC,EAAE,IAAI,CAAC,OAAO,CAAC,mBAAoB,CAAC,CAAC;QAEtC,IAAI,CAAC,oBAAoB,CAAC,GAAG,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;QAC9C,OAAO,CAAC,KAAK,CAAC,wCAAwC,IAAI,GAAG,CAAC,CAAC;IACjE,CAAC;IAED;;OAEG;IACH,eAAe,CAAC,IAAY;QAC1B,MAAM,QAAQ,GAAG,IAAI,CAAC,oBAAoB,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;QACrD,IAAI,QAAQ,EAAE,CAAC;YACb,aAAa,CAAC,QAAQ,CAAC,CAAC;YACxB,IAAI,CAAC,oBAAoB,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;YACvC,OAAO,CAAC,KAAK,CAAC,wCAAwC,IAAI,GAAG,CAAC,CAAC;QACjE,CAAC;IACH,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,kBAAkB,CAAC,IAAY;QAC3C,MAAM,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;QACxC,IAAI,CAAC,MAAM,IAAI,MAAM,CAAC,MAAM,KAAK,qBAAqB,CAAC,QAAQ,EAAE,CAAC;YAChE,OAAO;QACT,CAAC;QAED,IAAI,CAAC;YACH,gDAAgD;YAChD,oEAAoE;YACpE,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,uBAAuB,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;YAEpE,IAAI,SAAS,EAAE,CAAC;gBACd,IAAI,CAAC,kBAAkB,CAAC,IAAI,EAAE,qBAAqB,CAAC,OAAO,CAAC,CAAC;YAC/D,CAAC;iBAAM,CAAC;gBACN,IAAI,CAAC,kBAAkB,CAAC,IAAI,EAAE,qBAAqB,CAAC,SAAS,EAAE,qBAAqB,CAAC,CAAC;YACxF,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,kBAAkB,CAAC,IAAI,EAAE,qBAAqB,CAAC,SAAS,EAAE,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;QAChF,CAAC;IACH,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,uBAAuB,CAAC,MAAuB;QAC3D,uEAAuE;QACvE,8DAA8D;QAC9D,IAAI,MAAM,CAAC,OAAO,EAAE,CAAC;YACnB,IAAI,CAAC;gBACH,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,MAAM,CAAC,eAAe,CAAC,CAAC;gBAChD,MAAM,KAAK,GAAG,KAAK,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC,QAAQ,CAAC,EAAE;oBAC9C,KAAK,EAAE,MAAM;oBACb,OAAO,EAAE,IAAI;iBACd,CAAC,CAAC;gBAEH,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE;oBAC7B,IAAI,QAAQ,GAAG,KAAK,CAAC;oBAErB,MAAM,OAAO,GAAG,GAAG,EAAE;wBACnB,IAAI,CAAC,QAAQ,EAAE,CAAC;4BACd,QAAQ,GAAG,IAAI,CAAC;4BAChB,KAAK,CAAC,IAAI,EAAE,CAAC;wBACf,CAAC;oBACH,CAAC,CAAC;oBAEF,KAAK,CAAC,EAAE,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,EAAE;wBACxB,OAAO,EAAE,CAAC;wBACV,OAAO,CAAC,IAAI,KAAK,IAAI,CAAC,CAAC,CAAC,6CAA6C;oBACvE,CAAC,CAAC,CAAC;oBAEH,KAAK,CAAC,EAAE,CAAC,OAAO,EAAE,GAAG,EAAE;wBACrB,OAAO,EAAE,CAAC;wBACV,OAAO,CAAC,KAAK,CAAC,CAAC;oBACjB,CAAC,CAAC,CAAC;oBAEH,mBAAmB;oBACnB,UAAU,CAAC,GAAG,EAAE;wBACd,OAAO,EAAE,CAAC;wBACV,OAAO,CAAC,KAAK,CAAC,CAAC;oBACjB,CAAC,EAAE,IAAI,CAAC,CAAC;gBACX,CAAC,CAAC,CAAC;YACL,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,OAAO,KAAK,CAAC;YACf,CAAC;QACH,CAAC;QAED,+DAA+D;QAC/D,IAAI,MAAM,CAAC,GAAG,IAAI,MAAM,CAAC,OAAO,EAAE,CAAC;YACjC,qCAAqC;YACrC,OAAO,IAAI,CAAC,CAAC,0BAA0B;QACzC,CAAC;QAED,OAAO,IAAI,CAAC,CAAC,iDAAiD;IAChE,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,aAAa,CAAC,IAAY;QAC9B,MAAM,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;QACxC,IAAI,CAAC,MAAM,EAAE,CAAC;YACZ,OAAO,KAAK,CAAC;QACf,CAAC;QAED,OAAO,CAAC,IAAI,CAAC,qCAAqC,IAAI,GAAG,CAAC,CAAC;QAE3D,IAAI,CAAC;YACH,2CAA2C;YAC3C,MAAM,CAAC,mBAAmB,GAAG,CAAC,CAAC;YAC/B,MAAM,CAAC,MAAM,GAAG,qBAAqB,CAAC,OAAO,CAAC;YAE9C,MAAM,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC;YAEpC,MAAM,aAAa,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;YAC/C,MAAM,SAAS,GAAG,aAAa,EAAE,MAAM,KAAK,qBAAqB,CAAC,OAAO,CAAC;YAE1E,IAAI,SAAS,EAAE,CAAC;gBACd,OAAO,CAAC,IAAI,CAAC,sCAAsC,IAAI,GAAG,CAAC,CAAC;gBAC5D,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC;YAC9B,CAAC;iBAAM,CAAC;gBACN,OAAO,CAAC,IAAI,CAAC,iCAAiC,IAAI,GAAG,CAAC,CAAC;YACzD,CAAC;YAED,OAAO,SAAS,CAAC;QACnB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,wCAAwC,IAAI,MAAM,KAAK,EAAE,CAAC,CAAC;YACzE,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;IAED;;OAEG;IACH,OAAO;QACL,KAAK,MAAM,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,oBAAoB,EAAE,CAAC;YAC/C,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;QAC7B,CAAC;QACD,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,CAAC;QACvB,OAAO,CAAC,KAAK,CAAC,sCAAsC,CAAC,CAAC;IACxD,CAAC;IAED;;OAEG;IACH,gBAAgB;QACd,MAAM,OAAO,GAAG,IAAI,CAAC,kBAAkB,EAAE,CAAC;QAC1C,MAAM,OAAO,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,KAAK,qBAAqB,CAAC,OAAO,CAAC,CAAC,MAAM,CAAC;QACvF,MAAM,SAAS,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,KAAK,qBAAqB,CAAC,SAAS,CAAC,CAAC,MAAM,CAAC;QAC3F,MAAM,QAAQ,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,KAAK,qBAAqB,CAAC,QAAQ,CAAC,CAAC,MAAM,CAAC;QACzF,MAAM,OAAO,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,KAAK,qBAAqB,CAAC,OAAO,CAAC,CAAC,MAAM,CAAC;QAEvF,OAAO,sBAAsB,OAAO,aAAa,SAAS,eAAe,QAAQ,cAAc,OAAO,aAAa,OAAO,CAAC,MAAM,SAAS,CAAC;IAC7I,CAAC;CACF"}
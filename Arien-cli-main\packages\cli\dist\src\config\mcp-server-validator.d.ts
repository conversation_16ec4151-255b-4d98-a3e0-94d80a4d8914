/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */
import { MCPServerConfig } from '@arien/arien-cli-core';
export interface MCPServerValidationResult {
    isValid: boolean;
    error?: string;
    warnings?: string[];
    dependenciesInstalled: boolean;
    packageExists: boolean;
}
export interface DependencyCheckResult {
    command: string;
    available: boolean;
    version?: string;
    installInstructions?: string;
}
/**
 * Validates MCP server configurations and their dependencies
 */
export declare class MCPServerValidator {
    private static readonly REQUIRED_COMMANDS;
    private static readonly COMMAND_INSTALL_INSTRUCTIONS;
    /**
     * Validates a single MCP server configuration
     */
    static validateMCPServer(serverName: string, config: MCPServerConfig): Promise<MCPServerValidationResult>;
    /**
     * Validates basic MCP server configuration structure
     */
    private static validateBasicConfig;
    /**
     * Checks if a command is available in the system
     */
    static checkCommandAvailability(command: string): Promise<DependencyCheckResult>;
    /**
     * Checks if an npm or uvx package exists and can be installed
     */
    private static checkPackageExists;
    /**
     * Checks if an npm package exists
     */
    private static checkNpmPackage;
    /**
     * Checks if a uvx package exists
     */
    private static checkUvxPackage;
    /**
     * Validates all built-in MCP servers and returns only the valid ones
     */
    static validateBuiltInServers(servers: Record<string, MCPServerConfig>): Promise<Record<string, MCPServerConfig>>;
    /**
     * Checks system dependencies and provides installation instructions
     */
    static checkSystemDependencies(): Promise<DependencyCheckResult[]>;
    /**
     * Attempts to auto-install missing dependencies
     */
    static autoInstallDependencies(): Promise<{
        success: boolean;
        message: string;
    }>;
}

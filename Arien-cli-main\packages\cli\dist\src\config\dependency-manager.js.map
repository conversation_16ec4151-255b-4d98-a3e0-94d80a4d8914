{"version": 3, "file": "dependency-manager.js", "sourceRoot": "", "sources": ["../../../src/config/dependency-manager.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAEH,OAAO,EAAS,IAAI,EAAE,MAAM,eAAe,CAAC;AAC5C,OAAO,EAAE,SAAS,EAAE,MAAM,MAAM,CAAC;AACjC,OAAO,EAAE,QAAQ,EAAE,MAAM,IAAI,CAAC;AAE9B,MAAM,SAAS,GAAG,SAAS,CAAC,IAAI,CAAC,CAAC;AAiBlC;;GAEG;AACH,MAAM,OAAO,iBAAiB;IACpB,MAAM,CAAU,YAAY,GAAmC;QACrE,IAAI,EAAE;YACJ,IAAI,EAAE,SAAS;YACf,OAAO,EAAE,MAAM;YACf,SAAS,EAAE,KAAK;YAChB,mBAAmB,EAAE,0CAA0C;YAC/D,eAAe,EAAE,KAAK;SACvB;QACD,GAAG,EAAE;YACH,IAAI,EAAE,KAAK;YACX,OAAO,EAAE,KAAK;YACd,SAAS,EAAE,KAAK;YAChB,mBAAmB,EAAE,kEAAkE;YACvF,eAAe,EAAE,KAAK;SACvB;QACD,GAAG,EAAE;YACH,IAAI,EAAE,KAAK;YACX,OAAO,EAAE,KAAK;YACd,SAAS,EAAE,KAAK;YAChB,mBAAmB,EAAE,kEAAkE;YACvF,eAAe,EAAE,KAAK;SACvB;QACD,MAAM,EAAE;YACN,IAAI,EAAE,QAAQ;YACd,OAAO,EAAE,QAAQ;YACjB,SAAS,EAAE,KAAK;YAChB,mBAAmB,EAAE,yCAAyC;YAC9D,eAAe,EAAE,KAAK;SACvB;QACD,EAAE,EAAE;YACF,IAAI,EAAE,6BAA6B;YACnC,OAAO,EAAE,IAAI;YACb,SAAS,EAAE,KAAK;YAChB,mBAAmB,EAAE,yEAAyE;YAC9F,eAAe,EAAE,IAAI;SACtB;QACD,GAAG,EAAE;YACH,IAAI,EAAE,6BAA6B;YACnC,OAAO,EAAE,KAAK;YACd,SAAS,EAAE,KAAK;YAChB,mBAAmB,EAAE,4FAA4F;YACjH,eAAe,EAAE,IAAI;SACtB;QACD,GAAG,EAAE;YACH,IAAI,EAAE,KAAK;YACX,OAAO,EAAE,KAAK;YACd,SAAS,EAAE,KAAK;YAChB,mBAAmB,EAAE,uCAAuC;YAC5D,eAAe,EAAE,KAAK;SACvB;KACF,CAAC;IAEF;;OAEG;IACH,MAAM,CAAC,KAAK,CAAC,YAAY,CAAC,OAAe;QACvC,IAAI,CAAC;YACH,MAAM,EAAE,MAAM,EAAE,GAAG,MAAM,SAAS,CAAC,GAAG,OAAO,YAAY,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC,CAAC;YAC9E,OAAO;gBACL,SAAS,EAAE,IAAI;gBACf,OAAO,EAAE,MAAM,CAAC,IAAI,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE,sBAAsB;aAC9D,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,EAAE,SAAS,EAAE,KAAK,EAAE,CAAC;QAC9B,CAAC;IACH,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,KAAK,CAAC,oBAAoB;QAC/B,MAAM,OAAO,GAAmC,EAAE,CAAC;QAEnD,KAAK,MAAM,CAAC,GAAG,EAAE,GAAG,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,YAAY,CAAC,EAAE,CAAC;YAC3D,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;YACnD,OAAO,CAAC,GAAG,CAAC,GAAG;gBACb,GAAG,GAAG;gBACN,SAAS,EAAE,KAAK,CAAC,SAAS;gBAC1B,OAAO,EAAE,KAAK,CAAC,OAAO;aACvB,CAAC;QACJ,CAAC;QAED,OAAO,OAAO,CAAC;IACjB,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,KAAK,CAAC,sBAAsB;QACjC,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,oBAAoB,EAAE,CAAC;QAClD,OAAO,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;IAC9D,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,KAAK,CAAC,8BAA8B;QACzC,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,sBAAsB,EAAE,CAAC;QACpD,OAAO,OAAO,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,eAAe,CAAC,CAAC;IACpD,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,KAAK,CAAC,aAAa;QACxB,IAAI,CAAC;YACH,MAAM,eAAe,GAAG,QAAQ,EAAE,CAAC;YACnC,IAAI,cAAsB,CAAC;YAE3B,QAAQ,eAAe,EAAE,CAAC;gBACxB,KAAK,OAAO;oBACV,cAAc,GAAG,oFAAoF,CAAC;oBACtG,MAAM;gBACR,KAAK,QAAQ,CAAC;gBACd,KAAK,OAAO;oBACV,cAAc,GAAG,iDAAiD,CAAC;oBACnE,MAAM;gBACR;oBACE,OAAO;wBACL,OAAO,EAAE,KAAK;wBACd,OAAO,EAAE,gDAAgD,eAAe,EAAE;wBAC1E,UAAU,EAAE,IAAI;qBACjB,CAAC;YACN,CAAC;YAED,OAAO,CAAC,GAAG,CAAC,kBAAkB,CAAC,CAAC;YAChC,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,GAAG,MAAM,SAAS,CAAC,cAAc,EAAE,EAAE,OAAO,EAAE,MAAM,EAAE,CAAC,CAAC,CAAC,oBAAoB;YAErG,sBAAsB;YACtB,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;YAC9C,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;YAEhD,IAAI,OAAO,CAAC,SAAS,IAAI,QAAQ,CAAC,SAAS,EAAE,CAAC;gBAC5C,OAAO;oBACL,OAAO,EAAE,IAAI;oBACb,OAAO,EAAE,6BAA6B,OAAO,CAAC,OAAO,0BAA0B;oBAC/E,UAAU,EAAE,IAAI;iBACjB,CAAC;YACJ,CAAC;iBAAM,CAAC;gBACN,OAAO;oBACL,OAAO,EAAE,KAAK;oBACd,OAAO,EAAE,4IAA4I;oBACrJ,UAAU,EAAE,IAAI;iBACjB,CAAC;YACJ,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,OAAO,EAAE,yBAAyB,KAAK,EAAE;gBACzC,UAAU,EAAE,IAAI;aACjB,CAAC;QACJ,CAAC;IACH,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,KAAK,CAAC,gCAAgC;QAC3C,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,sBAAsB,EAAE,CAAC;QAEpD,IAAI,OAAO,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACzB,OAAO,4CAA4C,CAAC;QACtD,CAAC;QAED,MAAM,eAAe,GAAG,OAAO,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,eAAe,CAAC,CAAC;QACnE,MAAM,aAAa,GAAG,OAAO,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,eAAe,CAAC,CAAC;QAElE,IAAI,YAAY,GAAG,8BAA8B,CAAC;QAElD,IAAI,eAAe,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC/B,YAAY,IAAI,wBAAwB,CAAC;YACzC,eAAe,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE;gBAC5B,YAAY,IAAI,OAAO,GAAG,CAAC,IAAI,KAAK,GAAG,CAAC,OAAO,KAAK,CAAC;YACvD,CAAC,CAAC,CAAC;YACH,YAAY,IAAI,wCAAwC,CAAC;YACzD,YAAY,IAAI,kCAAkC,CAAC;QACrD,CAAC;QAED,IAAI,aAAa,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC7B,YAAY,IAAI,oCAAoC,CAAC;YACrD,aAAa,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE;gBAC1B,YAAY,IAAI,OAAO,GAAG,CAAC,IAAI,KAAK,GAAG,CAAC,OAAO,KAAK,CAAC;gBACrD,YAAY,IAAI,OAAO,GAAG,CAAC,mBAAmB,MAAM,CAAC;YACvD,CAAC,CAAC,CAAC;QACL,CAAC;QAED,OAAO,YAAY,CAAC;IACtB,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,KAAK,CAAC,6BAA6B,CAAC,UAAkB,EAAE,OAAe;QAK5E,MAAM,OAAO,GAAa,EAAE,CAAC;QAC7B,MAAM,YAAY,GAAa,EAAE,CAAC;QAElC,4BAA4B;QAC5B,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC;QACtD,IAAI,CAAC,YAAY,CAAC,SAAS,EAAE,CAAC;YAC5B,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YAEtB,MAAM,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,OAAO,KAAK,OAAO,CAAC,CAAC;YACtF,IAAI,OAAO,EAAE,CAAC;gBACZ,YAAY,CAAC,IAAI,CAAC,OAAO,CAAC,mBAAmB,CAAC,CAAC;YACjD,CAAC;iBAAM,CAAC;gBACN,YAAY,CAAC,IAAI,CAAC,WAAW,OAAO,yCAAyC,CAAC,CAAC;YACjF,CAAC;QACH,CAAC;QAED,0CAA0C;QAC1C,IAAI,OAAO,KAAK,KAAK,EAAE,CAAC;YACtB,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC;YAClD,IAAI,CAAC,SAAS,CAAC,SAAS,EAAE,CAAC;gBACzB,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;gBACrB,YAAY,CAAC,IAAI,CAAC,0CAA0C,CAAC,CAAC;YAChE,CAAC;QACH,CAAC;QAED,IAAI,OAAO,KAAK,KAAK,EAAE,CAAC;YACtB,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;YAC9C,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE,CAAC;gBACvB,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBACnB,YAAY,CAAC,IAAI,CAAC,yEAAyE,CAAC,CAAC;YAC/F,CAAC;QACH,CAAC;QAED,OAAO;YACL,KAAK,EAAE,OAAO,CAAC,MAAM,KAAK,CAAC;YAC3B,mBAAmB,EAAE,OAAO;YAC5B,YAAY;SACb,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,KAAK,CAAC,qBAAqB;QAMhC,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,oBAAoB,EAAE,CAAC;QACvD,MAAM,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;QAC1E,MAAM,SAAS,GAAG,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;QAE3E,MAAM,eAAe,GAAa,EAAE,CAAC;QAErC,IAAI,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACvB,MAAM,eAAe,GAAG,OAAO,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,eAAe,CAAC,CAAC;YACnE,IAAI,eAAe,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAC/B,eAAe,CAAC,IAAI,CAAC,uEAAuE,CAAC,CAAC;YAChG,CAAC;YAED,MAAM,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,eAAe,CAAC,CAAC;YAC3D,IAAI,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBACtB,eAAe,CAAC,IAAI,CAAC,2DAA2D,CAAC,CAAC;YACpF,CAAC;QACH,CAAC;QAED,OAAO;YACL,OAAO,EAAE,OAAO,CAAC,MAAM,KAAK,CAAC;YAC7B,OAAO,EAAE,GAAG,SAAS,CAAC,MAAM,IAAI,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,MAAM,yBAAyB;YACzF,OAAO,EAAE,YAAY;YACrB,eAAe;SAChB,CAAC;IACJ,CAAC"}
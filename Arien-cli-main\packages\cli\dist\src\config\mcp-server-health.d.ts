/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */
import { MCPServerConfig } from '@arien/arien-cli-core';
export declare enum MCPServerHealthStatus {
    HEALTHY = "healthy",
    UNHEALTHY = "unhealthy",
    UNKNOWN = "unknown",
    DISABLED = "disabled"
}
export interface MCPServerHealth {
    name: string;
    status: MCPServerHealthStatus;
    lastCheck: Date;
    consecutiveFailures: number;
    lastError?: string;
    config: MCPServerConfig;
}
export interface HealthCheckOptions {
    maxRetries: number;
    retryDelay: number;
    healthCheckInterval: number;
    disableAfterFailures: number;
}
/**
 * Monitors MCP server health and provides graceful error handling
 */
export declare class MCPServerHealthMonitor {
    private options;
    private healthMap;
    private healthCheckIntervals;
    private readonly defaultOptions;
    constructor(options?: Partial<HealthCheckOptions>);
    /**
     * Registers an MCP server for health monitoring
     */
    registerServer(name: string, config: MCPServerConfig): void;
    /**
     * Updates the health status of an MCP server
     */
    updateServerHealth(name: string, status: MCPServerHealthStatus, error?: string): void;
    /**
     * Gets the health status of an MCP server
     */
    getServerHealth(name: string): MCPServerHealth | undefined;
    /**
     * Gets health status of all registered servers
     */
    getAllServerHealth(): MCPServerHealth[];
    /**
     * Gets only healthy servers that can be used
     */
    getHealthyServers(): Record<string, MCPServerConfig>;
    /**
     * Starts periodic health checks for a server
     */
    startHealthCheck(name: string): void;
    /**
     * Stops health checks for a server
     */
    stopHealthCheck(name: string): void;
    /**
     * Performs a health check on a specific server
     */
    private performHealthCheck;
    /**
     * Basic connectivity check for MCP server
     */
    private checkServerConnectivity;
    /**
     * Attempts to recover a failed server
     */
    recoverServer(name: string): Promise<boolean>;
    /**
     * Cleanup all health checks
     */
    cleanup(): void;
    /**
     * Gets a summary of server health for logging/debugging
     */
    getHealthSummary(): string;
}

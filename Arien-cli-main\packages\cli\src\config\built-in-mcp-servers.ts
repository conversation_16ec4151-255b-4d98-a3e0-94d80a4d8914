/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */

import { MCPServerConfig } from '@arien/arien-cli-core';
import { MCPServerRegistry } from './mcp-server-registry.js';

/**
 * Built-in MCP servers that are automatically available without manual configuration.
 * These servers have the lowest priority and can be overridden by extensions or user settings.
 *
 * Note: This now uses the registry system to ensure only verified, compatible servers are loaded.
 */
// Use the registry system to get verified servers
export const BUILT_IN_MCP_SERVERS: Record<string, MCPServerConfig> = MCPServerRegistry.getVerifiedServers();

/**
 * MCP servers that require API keys or special configuration.
 * These are not included by default but can be easily enabled by users.
 */
export const CONFIGURABLE_MCP_SERVERS: Record<string, MCPServerConfig> = MCPServerRegistry.getConfigurableServers();

/**
 * Get the built-in MCP servers that should be automatically loaded.
 * This function now includes validation to ensure only compatible servers are returned.
 */
export async function getBuiltInMcpServers(): Promise<Record<string, MCPServerConfig>> {
  try {
    // Prevent authentication interference during MCP server validation
    const originalEnv = process.env.ARIEN_SKIP_AUTH;
    process.env.ARIEN_SKIP_AUTH = 'true';

    try {
      // Get compatible servers from the registry (validates dependencies)
      const compatibleServers = await MCPServerRegistry.getCompatibleServers();
      return compatibleServers;
    } finally {
      // Restore original environment
      if (originalEnv === undefined) {
        delete process.env.ARIEN_SKIP_AUTH;
      } else {
        process.env.ARIEN_SKIP_AUTH = originalEnv;
      }
    }
  } catch (error) {
    // Fallback to static servers if registry fails
    console.warn('Failed to get compatible servers from registry, falling back to static list:', error);
    return { ...BUILT_IN_MCP_SERVERS };
  }
}

/**
 * Get the built-in MCP servers synchronously (for backward compatibility).
 * This returns the static list without validation.
 */
export function getBuiltInMcpServersSync(): Record<string, MCPServerConfig> {
  return { ...BUILT_IN_MCP_SERVERS };
}

/**
 * Get configurable MCP servers that require additional setup.
 * These are provided for reference but not automatically loaded.
 */
export function getConfigurableMcpServers(): Record<string, MCPServerConfig> {
  return { ...CONFIGURABLE_MCP_SERVERS };
}

/**
 * Get experimental MCP servers (not included by default).
 */
export function getExperimentalMcpServers(): Record<string, MCPServerConfig> {
  return MCPServerRegistry.getExperimentalServers();
}

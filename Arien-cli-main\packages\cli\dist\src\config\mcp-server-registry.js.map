{"version": 3, "file": "mcp-server-registry.js", "sourceRoot": "", "sources": ["../../../src/config/mcp-server-registry.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAEH,OAAO,EAAE,eAAe,EAAE,MAAM,uBAAuB,CAAC;AAexD;;GAEG;AACH,MAAM,OAAO,iBAAiB;IACpB,MAAM,CAAU,gBAAgB,GAA2C;QACjF,WAAW,EAAE;YACX,IAAI,EAAE,WAAW;YACjB,MAAM,EAAE,IAAI,eAAe,CACzB,KAAK,EACL,CAAC,IAAI,EAAE,8BAA8B,CAAC,EACtC,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,qDAAqD,CACtD;YACD,QAAQ,EAAE,IAAI;YACd,YAAY,EAAE,IAAI,IAAI,CAAC,YAAY,CAAC;YACpC,QAAQ,EAAE,eAAe;YACzB,IAAI,EAAE,CAAC,SAAS,EAAE,eAAe,EAAE,QAAQ,CAAC;YAC5C,mBAAmB,EAAE;gBACnB,QAAQ,EAAE,CAAC,KAAK,CAAC;aAClB;SACF;QAED,YAAY,EAAE;YACZ,IAAI,EAAE,YAAY;YAClB,MAAM,EAAE,IAAI,eAAe,CACzB,KAAK,EACL,CAAC,IAAI,EAAE,wBAAwB,CAAC,EAChC,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,8CAA8C,CAC/C;YACD,QAAQ,EAAE,IAAI;YACd,YAAY,EAAE,IAAI,IAAI,CAAC,YAAY,CAAC;YACpC,QAAQ,EAAE,YAAY;YACtB,IAAI,EAAE,CAAC,SAAS,EAAE,YAAY,EAAE,SAAS,CAAC;YAC1C,mBAAmB,EAAE;gBACnB,QAAQ,EAAE,CAAC,KAAK,CAAC;aAClB;SACF;QAED,qBAAqB,EAAE;YACrB,IAAI,EAAE,qBAAqB;YAC3B,MAAM,EAAE,IAAI,eAAe,CACzB,KAAK,EACL,CAAC,IAAI,EAAE,kDAAkD,CAAC,EAC1D,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,4DAA4D,CAC7D;YACD,QAAQ,EAAE,IAAI;YACd,YAAY,EAAE,IAAI,IAAI,CAAC,YAAY,CAAC;YACpC,QAAQ,EAAE,UAAU;YACpB,IAAI,EAAE,CAAC,UAAU,EAAE,iBAAiB,EAAE,IAAI,CAAC;YAC3C,mBAAmB,EAAE;gBACnB,QAAQ,EAAE,CAAC,KAAK,CAAC;aAClB;SACF;QAED,YAAY,EAAE;YACZ,IAAI,EAAE,YAAY;YAClB,MAAM,EAAE,IAAI,eAAe,CACzB,KAAK,EACL,CAAC,IAAI,EAAE,gDAAgD,EAAE,IAAI,EAAE,GAAG,CAAC,EACnE,SAAS,EACT,OAAO,CAAC,GAAG,EAAE,EAAE,uCAAuC;YACtD,SAAS,EACT,SAAS,EACT,SAAS,EACT,IAAI,EAAE,+CAA+C;YACrD,SAAS,EACT,2CAA2C,CAC5C;YACD,QAAQ,EAAE,IAAI;YACd,YAAY,EAAE,IAAI,IAAI,CAAC,YAAY,CAAC;YACpC,QAAQ,EAAE,QAAQ;YAClB,IAAI,EAAE,CAAC,YAAY,EAAE,OAAO,EAAE,IAAI,CAAC;YACnC,mBAAmB,EAAE;gBACnB,QAAQ,EAAE,CAAC,KAAK,CAAC;aAClB;SACF;QAED,KAAK,EAAE;YACL,IAAI,EAAE,KAAK;YACX,MAAM,EAAE,IAAI,eAAe,CACzB,KAAK,EACL,CAAC,gBAAgB,EAAE,cAAc,EAAE,GAAG,CAAC,EACvC,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,+CAA+C,CAChD;YACD,QAAQ,EAAE,IAAI;YACd,YAAY,EAAE,IAAI,IAAI,CAAC,YAAY,CAAC;YACpC,QAAQ,EAAE,aAAa;YACvB,IAAI,EAAE,CAAC,KAAK,EAAE,iBAAiB,EAAE,aAAa,CAAC;YAC/C,mBAAmB,EAAE;gBACnB,QAAQ,EAAE,CAAC,KAAK,EAAE,KAAK,CAAC;aACzB;SACF;KACF,CAAC;IAEM,MAAM,CAAU,oBAAoB,GAA2C;QACrF,mFAAmF;QACnF,QAAQ,EAAE;YACR,IAAI,EAAE,QAAQ;YACd,MAAM,EAAE,IAAI,eAAe,CACzB,KAAK,EACL,CAAC,IAAI,EAAE,qCAAqC,CAAC,EAC7C,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,0CAA0C,CAC3C;YACD,QAAQ,EAAE,KAAK;YACf,YAAY,EAAE,IAAI,IAAI,CAAC,YAAY,CAAC;YACpC,QAAQ,EAAE,SAAS;YACnB,IAAI,EAAE,CAAC,QAAQ,EAAE,SAAS,EAAE,aAAa,CAAC;YAC1C,mBAAmB,EAAE;gBACnB,QAAQ,EAAE,CAAC,KAAK,CAAC;aAClB;SACF;KACF,CAAC;IAEM,MAAM,CAAU,oBAAoB,GAA2C;QACrF,yDAAyD;QACzD,cAAc,EAAE;YACd,IAAI,EAAE,cAAc;YACpB,MAAM,EAAE,IAAI,eAAe,CACzB,KAAK,EACL,CAAC,IAAI,EAAE,2CAA2C,CAAC,EACnD,EAAE,aAAa,EAAE,yBAAyB,EAAE,EAC5C,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,kDAAkD,CACnD;YACD,QAAQ,EAAE,IAAI;YACd,YAAY,EAAE,IAAI,IAAI,CAAC,YAAY,CAAC;YACpC,QAAQ,EAAE,QAAQ;YAClB,IAAI,EAAE,CAAC,QAAQ,EAAE,KAAK,EAAE,KAAK,CAAC;YAC9B,mBAAmB,EAAE;gBACnB,QAAQ,EAAE,CAAC,KAAK,CAAC;gBACjB,WAAW,EAAE,EAAE,aAAa,EAAE,UAAU,EAAE;aAC3C;SACF;KACF,CAAC;IAEF;;OAEG;IACH,MAAM,CAAC,kBAAkB;QACvB,MAAM,OAAO,GAAoC,EAAE,CAAC;QAEpD,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE,EAAE;YAC9D,OAAO,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC,MAAM,CAAC;QAC/B,CAAC,CAAC,CAAC;QAEH,OAAO,OAAO,CAAC;IACjB,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,sBAAsB;QAC3B,MAAM,OAAO,GAAoC,EAAE,CAAC;QAEpD,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE,EAAE;YAClE,OAAO,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC,MAAM,CAAC;QAC/B,CAAC,CAAC,CAAC;QAEH,OAAO,OAAO,CAAC;IACjB,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,sBAAsB;QAC3B,MAAM,OAAO,GAAoC,EAAE,CAAC;QAEpD,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE,EAAE;YAClE,OAAO,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC,MAAM,CAAC;QAC/B,CAAC,CAAC,CAAC;QAEH,OAAO,OAAO,CAAC;IACjB,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,cAAc,CAAC,IAAY;QAChC,OAAO,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC;YAC3B,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC;YAC/B,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,CAAC;IACzC,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,oBAAoB,CAAC,QAAgB;QAC1C,MAAM,UAAU,GAAG;YACjB,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,gBAAgB,CAAC;YACvC,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,oBAAoB,CAAC;YAC3C,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,oBAAoB,CAAC;SAC5C,CAAC;QAEF,OAAO,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,MAAM,CAAC,QAAQ,KAAK,QAAQ,CAAC,CAAC;IACnE,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,gBAAgB,CAAC,IAAc;QACpC,MAAM,UAAU,GAAG;YACjB,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,gBAAgB,CAAC;YACvC,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,oBAAoB,CAAC;YAC3C,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,oBAAoB,CAAC;SAC5C,CAAC;QAEF,OAAO,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,CAChC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAC5C,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,KAAK,CAAC,uBAAuB,CAClC,UAAkB;QAElB,MAAM,KAAK,GAAG,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,CAAC;QAC9C,IAAI,CAAC,KAAK,EAAE,CAAC;YACX,OAAO,EAAE,iBAAiB,EAAE,KAAK,EAAE,mBAAmB,EAAE,CAAC,kBAAkB,CAAC,EAAE,CAAC;QACjF,CAAC;QAED,MAAM,OAAO,GAAa,EAAE,CAAC;QAE7B,6BAA6B;QAC7B,IAAI,KAAK,CAAC,mBAAmB,EAAE,QAAQ,EAAE,CAAC;YACxC,KAAK,MAAM,OAAO,IAAI,KAAK,CAAC,mBAAmB,CAAC,QAAQ,EAAE,CAAC;gBACzD,IAAI,CAAC;oBACH,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,MAAM,CAAC,eAAe,CAAC,CAAC;oBAC/C,MAAM,EAAE,SAAS,EAAE,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,CAAC;oBAC3C,MAAM,SAAS,GAAG,SAAS,CAAC,IAAI,CAAC,CAAC;oBAElC,MAAM,SAAS,CAAC,GAAG,OAAO,YAAY,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC,CAAC;gBAC7D,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;oBACf,OAAO,CAAC,IAAI,CAAC,YAAY,OAAO,iBAAiB,CAAC,CAAC;gBACrD,CAAC;YACH,CAAC;QACH,CAAC;QAED,iCAAiC;QACjC,IAAI,KAAK,CAAC,mBAAmB,EAAE,WAAW,EAAE,CAAC;YAC3C,KAAK,MAAM,CAAC,MAAM,EAAE,WAAW,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,mBAAmB,CAAC,WAAW,CAAC,EAAE,CAAC;gBAC1F,IAAI,WAAW,KAAK,UAAU,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC;oBACvD,OAAO,CAAC,IAAI,CAAC,yBAAyB,MAAM,eAAe,CAAC,CAAC;gBAC/D,CAAC;YACH,CAAC;QACH,CAAC;QAED,OAAO;YACL,iBAAiB,EAAE,OAAO,CAAC,MAAM,KAAK,CAAC;YACvC,mBAAmB,EAAE,OAAO;SAC7B,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,2BAA2B,CAAC,UAAkB;QACnD,MAAM,KAAK,GAAG,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,CAAC;QAC9C,IAAI,CAAC,KAAK,EAAE,CAAC;YACX,OAAO,CAAC,8BAA8B,CAAC,CAAC;QAC1C,CAAC;QAED,MAAM,YAAY,GAAa,EAAE,CAAC;QAElC,wCAAwC;QACxC,IAAI,KAAK,CAAC,mBAAmB,EAAE,QAAQ,EAAE,CAAC;YACxC,KAAK,MAAM,OAAO,IAAI,KAAK,CAAC,mBAAmB,CAAC,QAAQ,EAAE,CAAC;gBACzD,QAAQ,OAAO,EAAE,CAAC;oBAChB,KAAK,KAAK;wBACR,YAAY,CAAC,IAAI,CAAC,yDAAyD,CAAC,CAAC;wBAC7E,MAAM;oBACR,KAAK,KAAK;wBACR,YAAY,CAAC,IAAI,CAAC,wFAAwF,CAAC,CAAC;wBAC5G,MAAM;oBACR,KAAK,KAAK;wBACR,YAAY,CAAC,IAAI,CAAC,uCAAuC,CAAC,CAAC;wBAC3D,MAAM;oBACR;wBACE,YAAY,CAAC,IAAI,CAAC,WAAW,OAAO,yCAAyC,CAAC,CAAC;gBACnF,CAAC;YACH,CAAC;QACH,CAAC;QAED,qCAAqC;QACrC,IAAI,KAAK,CAAC,mBAAmB,EAAE,WAAW,EAAE,CAAC;YAC3C,KAAK,MAAM,CAAC,MAAM,EAAE,WAAW,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,mBAAmB,CAAC,WAAW,CAAC,EAAE,CAAC;gBAC1F,IAAI,WAAW,KAAK,UAAU,EAAE,CAAC;oBAC/B,YAAY,CAAC,IAAI,CAAC,4BAA4B,MAAM,2CAA2C,CAAC,CAAC;gBACnG,CAAC;YACH,CAAC;QACH,CAAC;QAED,OAAO,YAAY,CAAC;IACtB,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,KAAK,CAAC,oBAAoB;QAC/B,MAAM,iBAAiB,GAAoC,EAAE,CAAC;QAE9D,qEAAqE;QACrE,2DAA2D;QAC3D,MAAM,0BAA0B,GAAG,CAAC,YAAY,CAAC,CAAC;QAElD,KAAK,MAAM,CAAC,IAAI,EAAE,KAAK,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,gBAAgB,CAAC,EAAE,CAAC;YAClE,IAAI,0BAA0B,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC;gBAC9C,OAAO,CAAC,KAAK,CAAC,kBAAkB,IAAI,uDAAuD,CAAC,CAAC;gBAC7F,SAAS;YACX,CAAC;YAED,IAAI,CAAC;gBACH,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,CAAC;gBAC9D,IAAI,YAAY,CAAC,iBAAiB,EAAE,CAAC;oBACnC,iBAAiB,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC,MAAM,CAAC;oBACvC,OAAO,CAAC,KAAK,CAAC,iBAAiB,IAAI,iBAAiB,CAAC,CAAC;gBACxD,CAAC;qBAAM,CAAC;oBACN,gFAAgF;oBAChF,oDAAoD;oBACpD,OAAO,CAAC,KAAK,CAAC,kBAAkB,IAAI,sEAAsE,YAAY,CAAC,mBAAmB,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;oBACzJ,iBAAiB,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC,MAAM,CAAC;gBACzC,CAAC;YACH,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,sDAAsD;gBACtD,OAAO,CAAC,KAAK,CAAC,kBAAkB,IAAI,+DAA+D,KAAK,EAAE,CAAC,CAAC;gBAC5G,iBAAiB,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC,MAAM,CAAC;YACzC,CAAC;QACH,CAAC;QAED,OAAO,iBAAiB,CAAC;IAC3B,CAAC"}
{"version": 3, "file": "built-in-mcp-servers.js", "sourceRoot": "", "sources": ["../../../src/config/built-in-mcp-servers.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAGH,OAAO,EAAE,iBAAiB,EAAE,MAAM,0BAA0B,CAAC;AAE7D;;;;;GAKG;AACH,kDAAkD;AAClD,MAAM,CAAC,MAAM,oBAAoB,GAAoC,iBAAiB,CAAC,kBAAkB,EAAE,CAAC;AAE5G;;;GAGG;AACH,MAAM,CAAC,MAAM,wBAAwB,GAAoC,iBAAiB,CAAC,sBAAsB,EAAE,CAAC;AAEpH;;;GAGG;AACH,MAAM,CAAC,KAAK,UAAU,oBAAoB;IACxC,IAAI,CAAC;QACH,oEAAoE;QACpE,MAAM,iBAAiB,GAAG,MAAM,iBAAiB,CAAC,oBAAoB,EAAE,CAAC;QACzE,OAAO,iBAAiB,CAAC;IAC3B,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,+CAA+C;QAC/C,OAAO,CAAC,IAAI,CAAC,8EAA8E,EAAE,KAAK,CAAC,CAAC;QACpG,OAAO,EAAE,GAAG,oBAAoB,EAAE,CAAC;IACrC,CAAC;AACH,CAAC;AAED;;;GAGG;AACH,MAAM,UAAU,wBAAwB;IACtC,OAAO,EAAE,GAAG,oBAAoB,EAAE,CAAC;AACrC,CAAC;AAED;;;GAGG;AACH,MAAM,UAAU,yBAAyB;IACvC,OAAO,EAAE,GAAG,wBAAwB,EAAE,CAAC;AACzC,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,yBAAyB;IACvC,OAAO,iBAAiB,CAAC,sBAAsB,EAAE,CAAC;AACpD,CAAC"}
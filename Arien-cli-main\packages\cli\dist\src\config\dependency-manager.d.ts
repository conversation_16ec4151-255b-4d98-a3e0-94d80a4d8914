/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */
export interface DependencyInfo {
    name: string;
    command: string;
    version?: string;
    installed: boolean;
    installInstructions: string;
    autoInstallable: boolean;
}
export interface InstallResult {
    success: boolean;
    message: string;
    dependency: string;
}
/**
 * Manages dependencies required for MCP servers
 */
export declare class DependencyManager {
    private static readonly DEPENDENCIES;
    /**
     * Checks if a command is available in the system
     */
    static checkCommand(command: string): Promise<{
        available: boolean;
        version?: string;
    }>;
    /**
     * Checks all dependencies and returns their status
     */
    static checkAllDependencies(): Promise<Record<string, DependencyInfo>>;
    /**
     * Gets missing dependencies
     */
    static getMissingDependencies(): Promise<DependencyInfo[]>;
    /**
     * Gets auto-installable missing dependencies
     */
    static getAutoInstallableDependencies(): Promise<DependencyInfo[]>;
    /**
     * Attempts to auto-install uv (the main auto-installable dependency)
     */
    static autoInstallUv(): Promise<InstallResult>;
    /**
     * Generates installation instructions for missing dependencies
     */
    static generateInstallationInstructions(): Promise<string>;
    /**
     * Validates that required dependencies for specific MCP servers are available
     */
    static validateMcpServerDependencies(serverName: string, command: string): Promise<{
        valid: boolean;
        missingDependencies: string[];
        instructions: string[];
    }>;
    /**
     * Checks system health for MCP server dependencies
     */
    static getSystemHealthReport(): Promise<{
        healthy: boolean;
        summary: string;
        details: Record<string, DependencyInfo>;
        recommendations: string[];
    }>;
}
